angular.module('SportWrench')

.directive('maxTime', function($document, $timeout) {
    return {
        restrict: 'A',
        link: function (scope, elem, attrs) {

            scope.$watch('endedType.id', function(newVal, oldVal) {

                switch (newVal) {
                    case 1: 
                        delete attrs.max;
                        delete attrs.min;
                        break;
                    case 2: 
                        attrs.max = 60;
                        attrs.min = 0;
                        break;
                    case 3:
                        attrs.max = 99;
                        attrs.min = 0;
                        break;

                }
                
            });

        }
    };
});
