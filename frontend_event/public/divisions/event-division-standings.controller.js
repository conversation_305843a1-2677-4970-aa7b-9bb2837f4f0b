angular.module('SportWrench')

.controller('Public.Events.DivisionStandingsController', divisionStandingsController);

function divisionStandingsController($scope, $rootScope, $state, $stateParams, eswService, currentEvent, currentDivision, currentStandings)
{
    $scope.isDoubles   = currentEvent.registration_method == 'doubles' ? true : false;
    $scope.hideSeeds   = currentEvent.hide_seeds;
    $scope.hideCurrent = false;

    $scope.standings = currentStandings.teams;

    $scope.isTitleHidden = isTitleHidden;
    $scope.isAllStandingsUnavailable = isAllStandingsUnavailable;

    $rootScope.pageTitle = 'Standings';

    $scope.currentDiv = currentDivision.division_id;

    $scope.divisions = eswService.getCurrentDivisions();

    $scope.changeDivision = function() {
        $state.go('events.event.divisions.division.divisionstandings', {division: $scope.currentDiv});
    };

    $scope.orderColumn = 'rank';

    $scope.orderData = function(colName) {
        $scope.orderColumn = colName;
        $scope.reverseSort = !$scope.reverseSort;
    };

    $scope.getCurrentValue = function (standing) {
        return (showCurrent(standing)) ? standing.cur_num : '-';
    };

    $scope.showCurrentCol = function (division_obj) {
        let show = _.some(division_obj, (standing) => showCurrent(standing));

        $scope.hideCurrent = !show;

        return show;
    };

    $scope.showPointsColumn = function (standing) {
        return standing.rank && standing.info && !_.isUndefined(standing.info.points) && !_.isNull(standing.info.points);
    }

    $scope.isDivisionStandingsHidden = function () {
        return currentEvent.teams_settings &&  currentEvent.teams_settings.hide_standings && !currentDivision.is_game_over;
    };

    function showCurrent(standing) {
        return !standing.rank && (standing.matches_won || standing.matches_lost) && !$scope.hideSeeds;
    }

    function isTitleHidden (division_name) {
        return division_name === 'None' && Object.keys($scope.standings).length == 1;
    }

    function isAllStandingsUnavailable () {
        if($scope.standings) {
            return Object.keys($scope.standings).every(function(objectKey) {
                return $scope.standings[objectKey].length === 0;
            });
        } else {
            return false;
        }

    }

}
