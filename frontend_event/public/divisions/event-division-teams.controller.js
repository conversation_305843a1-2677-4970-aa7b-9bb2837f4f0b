angular.module('SportWrench')

.controller('Public.Events.DivisionTeamsController', divisionTeamsController);

function divisionTeamsController($scope, $filter, $rootScope, $state, $stateParams, eswService, currentEvent, divisionsList, currentDivision, divisionTeams)
{
    $scope.currentDiv = currentDivision.division_id;

    $scope.divisions = eswService.getCurrentDivisions();

    $scope.changeDivision = function() {
        $state.go('events.event.divisions.division', {division: $scope.currentDiv});
    };

    $scope.teams = undefined;

    $scope.isSchedulePublished = currentEvent.schedule_published;

    $scope.hasFlowChart = currentDivision.has_flow_chart;

    $scope.isBSQ = currentEvent.event_id == 'ca22c3ec6' || currentEvent.event_id == '1674a8440';

    divisionsList = $filter('orderBy')(divisionsList, 'name');

    divisionsList.forEach(function(division, i, arr) {
        if ($stateParams.division == division.division_id) {
            if (i > 0) {
                $scope.prev = {
                    id: arr[i-1].division_id,
                    name: arr[i-1].name
                };
            }
            if (i < divisionsList.length - 1) {
                $scope.next = {
                    id: arr[i+1].division_id,
                    name: arr[i+1].name
                };
            }
        }
    });

    $scope.teams = divisionTeams;

    $scope.teamsCount = $scope.teams.length;

    if (angular.isArray($scope.teams)) {
        $scope.teams.forEach(function(team) {
            try {
                team.results = angular.fromJson(team.results);
            } catch (e) {
                console.log('JSON Error: team.results = ', team.results);
            }
        });
    }

    $rootScope.pageTitle = 'Division ' + currentDivision.name + ' Teams';
    $scope.title = currentDivision.name;

    $scope.isHideStandings = function() {
        return $scope.currentEvent.teams_settings && $scope.currentEvent.teams_settings.hide_standings && !currentDivision.is_game_over
    }

}
