angular.module('SportWrench')

.controller('Public.Events.DivisionBracketController', BracketController);

function BracketController($scope, $rootScope, $filter, currentBracket, bracketMatches, currentDivision) {
	$scope.bracket = currentBracket;
	$scope.bracket.tpl_name = $scope.bracket.team_count + 't' + ($scope.bracket.consolation == 1 ? "wc" : "nc");

	angular.forEach(bracketMatches.matches, function(match) {
		try {
			match.source = angular.fromJson(match.source);
			
			match.footnote_team1 = match.footnote_team1 ? match.footnote_team1 : '';
			match.footnote_team2 = match.footnote_team2 ? match.footnote_team2 : '';
			
			if (match.team1_name) {
				match.team1_name = match.team1_name + match.footnote_team1;
			}

			if (match.team2_name) {
				match.team2_name = match.team2_name + match.footnote_team2;
			}

			if (match.team2_pool_name) {
				match.team2_pool_name = match.team2_pool_name + match.footnote_team2;
			} 

			if (match.team1_pool_name) {
				match.team1_pool_name = match.team1_pool_name + match.footnote_team1;
			}

			if (match.source.team1.name) {
				match.source.team1.name = match.source.team1.name + match.footnote_team1;
			}

			if (match.source.team2.name) {
				match.source.team2.name = match.source.team2.name + match.footnote_team2;
			}
		} catch (e) {
			console.log('JSON Error: match.source = ', match.source);
		}
		match.date_start = $filter('UTCdate')(new Date(parseInt(match.date_start)), 'ddd h:mma');
	});
	bracketMatches.matches.unshift({});
	$scope.bracket.matches = bracketMatches.matches;
	$scope.bracket.pool = bracketMatches.pool;
	try {
		$scope.bracket.pool.pb_seeds = angular.fromJson($scope.bracket.pool.pb_seeds || '{}');
		$scope.bracket.pool.pb_finishes = angular.fromJson($scope.bracket.pool.pb_finishes || '{}');

		if (Object.keys($scope.bracket.pool.pb_finishes).indexOf('team_ids') >= 0) {
            delete $scope.bracket.pool.pb_finishes.team_ids;
        }
	} catch (e) {
		console.log('JSON Error: bracket.pool = ', e);
	}

	$scope.bracket.name = currentDivision.short_name + ' > ' + bracketMatches.display_name;
	$rootScope.pageTitle = $scope.bracket.name;
}
