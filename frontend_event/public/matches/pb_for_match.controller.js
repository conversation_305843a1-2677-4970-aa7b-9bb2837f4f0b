angular.module('SportWrench')

.controller('Public.Events.PbForMatch', PbForMatch);

function PbForMatch($scope, $rootScope, $stateParams, $state, eswService)
{
	
	eswService.getMatchParams($stateParams.uuid, function(response) {
		$scope.info = response.data;
	
		if ($scope.info.is_pool) {
			$state.go('events.event.divisions.division.pools.pooldetails.schedule', {
			    event: $scope.info.event_id, 
			    division: $scope.info.division_id,
			    pool: $scope.info.pool_bracket_id
			});
		} else {
			$state.go('events.event.divisions.division.divisionbrackets.bracket', {
			    event: $scope.info.event_id, 
			    division: $scope.info.division_id,
			    bracket: $scope.info.pool_bracket_id
			});
		}
	});

}
