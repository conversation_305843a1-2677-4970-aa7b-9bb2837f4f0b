angular.module('SportWrench')

.controller('Public.Events.PoolDetailsController', PoolDetailsController);

function PoolDetailsController($scope, $rootScope, $stateParams, eswService, currentDivision, currentPool, $filter, $state, $window) {
    $scope.divisionName = currentDivision.name;
    $scope.divisionShortName = currentDivision.short_name;

    $scope.pool = currentPool;

    $scope.title = currentDivision.name + ' > ' + $scope.pool.display_name;
    $rootScope.pageTitle = $scope.title;

    if ($state.current.name == 'events.event.divisions.division.pools.pooldetails.schedule') {
        $scope.tabs = [{active: true}, {active: false}, {active: false}, {active: false}];
    } else if ($state.current.name == 'events.event.divisions.division.pools.pooldetails.results') {
        $scope.tabs = [{active: false}, {active: true}, {active: false}, {active: false}];
    } else if ($state.current.name == 'events.event.divisions.division.pools.pooldetails.standings') {
        $scope.tabs = [{active: false}, {active: false}, {active: true}, {active: false}];
    } else if ($state.current.name == 'events.event.divisions.division.pools.pooldetails.future') {
        $scope.tabs = [{active: false}, {active: false}, {active: false}, {active: true}];
    }

    $scope.hasShedule = $scope.pool.upcoming_matches ? true : false;

    $scope.hasResults = $scope.pool.results ? true : false;

    $scope.hasFuture = function () {
        try {
            $scope.pool.pb_finishes = angular.fromJson($scope.pool.pb_finishes);
        } catch (e) {
            console.log('JSON Error: pb_finishes');
        }        

        return ($scope.pool.pb_finishes && Object.keys($scope.pool.pb_finishes).length !== 0);
    };

    if ($scope.pool.standings) {
        try {
            if ($scope.pool.standings[0] != undefined && 
                $scope.pool.standings[0].pb_stats && 
                $scope.pool.standings[0].pb_stats['1st'] == undefined) {
                $scope.pool.standings[0].pb_stats = angular.fromJson($scope.pool.standings[0].pb_stats);
            }
        } catch (e) {
            console.log('JSON Error: currentStandings.pb_stats = ', $scope.pool.standings[0].pb_stats);
        }
    }

    $scope.hasStandings = function () {
        if (!$scope.pool.standings || $scope.pool.standings[0] == undefined) {
            return false;
        }
        return ($scope.pool.standings[0].pb_stats && Object.keys($scope.pool.standings[0].pb_stats).length !== 0);
    };

    $scope.setTab = function(tab) {
        $state.go('events.event.divisions.division.pools.pooldetails.' + tab, {
            event: $stateParams.event, 
            division: $stateParams.division,
            pool: $stateParams.pool
        });
    };

    $scope.ifAvailableGoTo = function (standing, state) {
        if (standing.team_id != 0) {
            $state.go(state, {team: standing.team_id});
        }
    };

    /*if ($scope.hasShedule) {
        $scope.tabs = [{active: true}, {active: false}, {active: false}, {active: false}];

        $state.go('events.event.divisions.division.pools.pooldetails.schedule', {
            event: $stateParams.event, 
            division: $stateParams.division,
            pool: $stateParams.pool
        }, {
            inherit: true,
            notify: false
        });
    } else if ($scope.hasResults) {
        $scope.tabs = [{active: false}, {active: true}, {active: false}, {active: false}];

        $state.go('events.event.divisions.division.pools.pooldetails.results', {
            event: $stateParams.event, 
            division: $stateParams.division,
            pool: $stateParams.pool
        }, {
            inherit: true,
            notify: false
        });
    }*/

    $scope.hasFutureMatches = false;


    //schedule
    if ($scope.pool.upcoming_matches) {
        $scope.pool.upcoming_matches.forEach(function(match) {
            match.start = new Date(parseInt(match.date_start));
            try {
                match.source = angular.fromJson(match.source);
            } catch (e) {
                console.log('JSON Error: match.source = ', match.source);
            }
        });
    }

    //results
    if ($scope.pool.results) {
        $scope.pool.results.forEach(function(res) {
            try {
                res.results = angular.fromJson(res.results);

                if (res.results.team1 && 
                    res.results.team1.scores && 
                    res.results.team1.scores.indexOf(',undefined-undefined') != -1) {
                    res.results.team1.scores = res.results.team1.scores.replace(',undefined-undefined', '');
                }

                if (res.results.team2 && 
                    res.results.team2.scores && 
                    res.results.team2.scores.indexOf(',undefined-undefined') != -1) {
                    res.results.team2.scores = res.results.team2.scores.replace(',undefined-undefined', '');
                }
            } catch (e) {
                console.log('JSON Error: res.results = ', res.results);
            }
        });
    }

    //future
    $scope.isMatchAvailable = function (future) {
        return (future.next_match && Object.keys(future.next_match).length !== 0 && future.next_match.match_id);
    };

    $scope.isRefAvailable = function (future) {
        return (future.next_ref && Object.keys(future.next_ref).length !== 0 && future.next_ref.match_id);
    };

    $scope.isFuturePlaceholderShown = function (future) {
        return !future.next_match.match_id && !future.next_ref.match_id;
    };

    $scope.isPlaceholderAvailable = function (future) {
        return (future.next_match && Object.keys(future.next_match).length !== 0 && future.next_match.display_name);
    };

    $scope.isNothingAssigned = function (future) {
        return $scope.isFuturePlaceholderShown(future) && !future.next_match.display_name;
    };

    if ($scope.pool.pb_finishes) {
        try {
            $scope.pool.pb_finishes = angular.fromJson($scope.pool.pb_finishes);
        } catch (e) {
            console.log('JSON Error: $scope.pool.pb_finishes = ', $scope.pool.pb_finishes);
        }
        delete $scope.pool.pb_finishes.team_ids;

        for (var future in $scope.pool.pb_finishes) {
            if ($scope.pool.pb_finishes[future].next_match && Object.keys($scope.pool.pb_finishes[future].next_match).length) {
                $scope.hasFutureMatches = true;
                $scope.pool.pb_finishes[future].next_match.start = new Date(parseInt($scope.pool.pb_finishes[future].next_match.secs_start*1000));
            }
            if ($scope.pool.pb_finishes[future].next_ref && Object.keys($scope.pool.pb_finishes[future].next_ref).length) {
                $scope.pool.pb_finishes[future].next_ref.start = new Date(parseInt($scope.pool.pb_finishes[future].next_ref.secs_start*1000));
            }
        }
    }



    //standings
    $scope.orderData = function(colName) {
        $scope.orderColumn = colName;
        $scope.reverseSort = !$scope.reverseSort;
    };

    if ($scope.pool.standings) {
        try {
            if ($scope.pool.standings[0] != undefined && 
                $scope.pool.standings[0].pb_stats && 
                $scope.pool.standings[0].pb_stats['1st'] == undefined) {
                    $scope.pool.standings[0].pb_stats = angular.fromJson($scope.pool.standings[0].pb_stats);
            }

            $scope.pool.standing_keys = Object.keys($scope.pool.standings[0].pb_stats);
            $scope.isRank = $scope.pool.standings[0].pb_stats['1st'].rank ? true : false;
            
            for (var i in $scope.pool.standings.pb_stats) {
                $scope.pool.standings.pb_stats[i].starting = i;
            }

            var isEveryRankZero = _.some($scope.pool.standings[0].pb_stats, function (stat) {
                return stat.rank == 0;
            });

            if ($scope.isRank && isEveryRankZero === false) {
                $scope.orderColumn = 'rank';
            }

            $scope.num = function (str) {
                if(!str) return '';
                return $filter('number')(+str, 3);
            }
        } catch (e) {
            console.log('JSON Error: currentStandings.pb_stats = ', $scope.pool.standings.pb_stats);
        }
    }


    if ($scope.pool.pb_stats) {
        try {
            $scope.pool.pb_stats = angular.fromJson($scope.pool.pb_stats);
        } catch (e) {
            console.log('JSON Error: $scope.pool.pb_stats = ', $scope.pool.pb_stats);
        }

        $scope.isFinished = function(key) {
            var newKey = $filter('suffix')(key);
            var rank = $scope.pool.pb_stats[newKey].rank;
            return rank ? true : false;
        }
    }

}
