angular.module('SportWrench')

.controller('Public.Events.PoolDetailsSheduleController', PoolDetailsController);

function PoolDetailsController($scope, $rootScope, $stateParams, eswService, currentDivision, currentPool, $filter, $state, $window) {
    $scope.pool = currentPool;

    $scope.title = currentDivision.name + ' > ' + $scope.pool.display_name;
    $rootScope.pageTitle = $scope.title + " Shedule";

    if ($scope.pool.upcoming_matches) {
        $scope.pool.upcoming_matches.forEach(function(match) {
            match.start = new Date(parseInt(match.date_start));
            try {
                match.source = angular.fromJson(match.source);
            } catch (e) {
                console.log('JSON Error: match.source = ', match.source);
            }
        });
    } else {
        $state.go('events.event.divisions.division.pools.pooldetails.results', {
            event: $stateParams.event, 
            division: $stateParams.division,
            pool: $stateParams.pool
        });
    }

}