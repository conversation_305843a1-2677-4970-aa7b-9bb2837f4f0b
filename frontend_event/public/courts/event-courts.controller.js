angular.module('SportWrench')

.controller('Public.Events.CourtsController', CourtsController);

function CourtsController($scope, $rootScope, $stateParams, eswService, eventsList)
{
    $scope.courts = [];

    $rootScope.pageTitle = 'Courts';

    if (eventsList && eventsList.length) {
        eventsList.forEach(function(event) {
            if (event.event_id == $stateParams.event) {
                if (typeof event.divisions !== 'undefined') {
                    $scope.courts = event.courts;
                } else {
                    eswService.getEventCourts($stateParams.event, function(response) {
                        $scope.courts = response.data;
                    });
                }                
            }
        });
    }

    $scope.saveCourtName = function(name) {
        $rootScope.currentCourtName = name;
        // $rootScope.$storage.lastValue = name;
    };
}
