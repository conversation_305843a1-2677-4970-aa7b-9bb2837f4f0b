angular.module('SportWrench')

.controller('Public.Events.EventsController', eventsController);

function eventsController($scope, eswService, $rootScope, eventsList, $localStorage, $state, $stateParams) {
	$rootScope.pageTitle = "Events";

	$scope.events = eventsList;

	if ($localStorage.loadLastEvent && 
		$localStorage.lastEventId && 
		$localStorage.allowLastEventLoading &&
		$localStorage.lastEventEnds > (new Date()).valueOf()) {
		$state.go('events.event', {
		    event: $localStorage.lastEvent.event_id
		});
	}
}
