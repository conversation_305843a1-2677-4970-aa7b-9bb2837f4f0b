angular.module('SportWrench').directive('enteredTeamsList', function (eswService) {
	return {
		restrict: 'E',
		scope: {
			eventId: "@"
		},
		templateUrl: 'public/events/entered-teams.html',
		link: function (scope) {
			eswService.getEventDivisionsList(scope.eventId)
			.then(function (resp) {
				scope.event_divisions = resp && resp.data && resp.data.divisions || [];
			}, function () {
				scope.event_divisions = []
			})

			scope.checkDivision = function (d) {
		        d.checked = !d.checked
			}

			scope.emptyTeamsList = function (d) {
				return !(d.teams && d.teams.length)
			}
		}
	}
})