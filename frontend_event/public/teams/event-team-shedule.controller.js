angular.module('SportWrench')

.controller('Public.Events.TeamSheduleController', TeamSheduleController);

function TeamSheduleController($scope, $rootScope, $stateParams, eswService, currentTeam, $state, currentPools)
{
    $scope.team = currentTeam;

    if (currentTeam.bracket_finishes) {
        $scope.finishes = JSON.parse(currentTeam.bracket_finishes);
    }

    $rootScope.pageTitle = "Team " + currentTeam.team_name + " Schedule";
    $scope.title = currentTeam.team_name;

    if ($scope.team && $scope.team.upcoming) {
        $scope.team.upcoming.forEach(function(match) {
            match.start = new Date(parseInt(match.date_start));
            match.round = match.pool_name.substr(0,2);

            if(currentPools) {
                currentPools.forEach(pool => {
                    if(pool.settings && pool.uuid === match.pool_bracket_id) {
                        pool.settings = angular.fromJson(pool.settings);

                        if (!pool.settings['PlayAllSets']) {
                            match.matches_played_exp = 'Best of ' + pool.settings['SetCount'];
                        } else {
                            match.matches_played_exp
                                = pool.settings['SetCount'] + ' to ' + pool.settings['WinningPoints'];
                        }
                    }
                })
            }
        });
    } else {
        /*
        problem with $state.go()
        use case: 
        1. user initially is on : events/8506279a7/divisions/1149/teams/16157/results
        2. user MANUALLY changes to : events/8506279a7/divisions/1149/teams/16157/schedule
        3. $scope.team.upcoming is null, so we are here
        4. trying to $state.go() to events/8506279a7/divisions/1149/teams/16157
        6. this DOES NOT fire $stateProvider.state('events.event.divisions.division.divisionteams.divisionteam') 
           because params have not changed and state change is not detected (maybe because we are in middle of transitions)

        -- work around : use transitionTo with reload : true ----
        */

        $state.transitionTo('events.event.divisions.division.divisionteams.divisionteam', {
            event: $stateParams.event,
            division: $stateParams.division,
            team: $stateParams.team
        }, { reload : true });

    }

    $scope.isEmpty = function(ob) {
        return _.isEmpty(ob);
    }

    $scope.showFutureBracketMatches = function() {
        if ($scope.team && $scope.team.upcoming && $scope.team.upcoming.length === 1) {
            return $scope.team.upcoming[0].match_type !== 'ref' && !$scope.isEmpty($scope.finishes);
        }

        return !$scope.isEmpty($scope.finishes);
    }
}
