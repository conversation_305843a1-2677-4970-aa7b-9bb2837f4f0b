angular.module('SportWrench')

.controller('Public.Events.TeamController', TeamController);

function TeamController($scope, $rootScope, $stateParams, eswService, currentTeam, $state, currentEvent)
{
    $scope.team = currentTeam;

    $rootScope.pageTitle = "Team - " + currentTeam.team_name;
    $scope.title = currentTeam.team_name;
    $scope.USAV  = currentTeam.organization_code;

    $scope.isDoublesTeams = (currentEvent.registration_method === 'doubles');

    $scope.hasResults = ($scope.team.results && $scope.team.results.length);

    $scope.showManualClub = !$scope.isDoublesTeams &&
        !$scope.hasClubs &&
        currentEvent.teams_settings.manual_club_names &&
        currentTeam.manual_club_name;

    $scope.hasShedule = !$scope.team.upcoming ? false : true;

    $scope.hasRoster = function() {
        const rules = [
            $scope.team.athletes && $scope.team.athletes.length,
            $scope.team.staff && $scope.team.staff.length,
            currentEvent.has_rosters
        ];

        return rules.every(rule => rule);
    }

    $scope.hasClubs = $rootScope.currentEvent && $rootScope.currentEvent.has_clubs;

    if ($scope.team && $scope.team.upcoming) {
        $scope.team.upcoming.forEach(function(match) {
            match.start = new Date(parseInt(match.date_start));
        });
    }

    if ($scope.team && $scope.team.results && $scope.team.results.length) {
        $scope.team.results.forEach(function(res) {
            try {
                res.results = angular.fromJson(res.results);
            } catch (e) {
                console.log('JSON Error: res.results = ', res.results);
            }
        });
    }

    console.log($state)

    if($state.is('events.event.divisions.division.divisionteams.divisionteam')) { 
        console.log('here we are')
        console.log('has shedule ' + $scope.hasShedule)
        console.log('has results ' + $scope.hasResults)

        if ($scope.hasShedule) {
            $scope.tabs = [{active: true}, {active: false}, {active: false}, {active: false}];
            $state.go('events.event.divisions.division.divisionteams.divisionteam.schedule', {
                event: $stateParams.event,
                division: $stateParams.division,
                team: $stateParams.team
            });
        } else if ($scope.hasResults) {
            console.info('Moving to results')
            $scope.tabs = [{active: false}, {active: false}, {active: true}, {active: false}];
            $state.go('events.event.divisions.division.divisionteams.divisionteam.results', {
                event: $stateParams.event,
                division: $stateParams.division,
                team: $stateParams.team
            });
        } else {
            $scope.tabs = [{active: false}, {active: false}, {active: false}, {active: true}];
            $state.go('events.event.divisions.division.divisionteams.divisionteam.roster', {
                event: $stateParams.event,
                division: $stateParams.division,
                team: $stateParams.team
            });
        }
    } else {
        if($state.is('events.event.divisions.division.divisionteams.divisionteam.schedule')) {
            $scope.tabs = [{active: true}, {active: false}, {active: false}, {active: false}];
        } else if($state.is('events.event.divisions.division.divisionteams.divisionteam.results')) {
            $scope.tabs = [{active: false}, {active: false}, {active: true}, {active: false}];
        } else if($state.is('events.event.divisions.division.divisionteams.divisionteam.roster')) {
            $scope.tabs = [{active: false}, {active: false}, {active: false}, {active: true}];
        }
    }

    $scope.setTab = function(tab) {
        $state.go('events.event.divisions.division.divisionteams.divisionteam.' + tab, {
            event: $stateParams.event,
            division: $stateParams.division,
            team: $stateParams.team
        });
    };
}
